#!/usr/bin/env python3
import os
import re

def fix_pom_file(file_path):
    """Fix the <n> tags in pom.xml files"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace <n>content</n> with <name>content</name>
        fixed_content = re.sub(r'<n>([^<]*)</n>', r'<name>\1</name>', content)
        
        if content != fixed_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            print(f"Fixed: {file_path}")
            return True
        else:
            print(f"No changes needed: {file_path}")
            return False
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def main():
    # Find all pom.xml files
    pom_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file == 'pom.xml':
                pom_files.append(os.path.join(root, file))
    
    print(f"Found {len(pom_files)} pom.xml files")
    
    fixed_count = 0
    for pom_file in pom_files:
        if fix_pom_file(pom_file):
            fixed_count += 1
    
    print(f"Fixed {fixed_count} files")

if __name__ == "__main__":
    main()
