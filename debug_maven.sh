#!/bin/bash

echo "=== Maven Debug Script ==="
echo "Current directory: $(pwd)"
echo "Java version:"
java -version
echo ""
echo "Maven version:"
mvn -version
echo ""
echo "Checking pom.xml exists:"
ls -la pom.xml
echo ""
echo "Trying Maven validate with timeout..."
# Use perl to implement timeout since timeout command is not available
perl -e 'alarm 30; exec @ARGV' mvn validate -s temp-settings.xml 2>&1
echo "Exit code: $?"
echo ""
echo "=== End Debug ==="
